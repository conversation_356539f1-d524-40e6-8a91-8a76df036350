"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { executeTestCase, getTestExecutionHistoryDetails, getTestCaseById } from "@/lib/api";
import type { TestExecutionHistoryData, TestCaseExecutionResponse } from "@/lib/types";
import { ExecutionDetailsView } from "@/components/execution/ExecutionDetailsView";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ArrowLeft, AlertCircle, RefreshCw, Play } from "lucide-react"; // Added Play icon
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Added Card components

function ExecutionLoadingSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10 w-1/3" /> {/* Header */}
      <Skeleton className="h-12 w-full rounded-md" /> {/* Tabs List */}
      <div className="mt-4 p-4 rounded-lg border bg-card">
        <Skeleton className="h-6 w-1/4 mb-4" /> {/* Tab Title */}
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    </div>
  );
}

export default function ExecuteTestPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();

  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;
  
  const historyPath = searchParams.get("historyPath");
  const executionIdFromQuery = searchParams.get("executionId"); // Used for fresh executions

  const [localHistoryData, setLocalHistoryData] = useState<TestExecutionHistoryData | null>(null);
  const [isLoadingExecution, setIsLoadingExecution] = useState(false);
  const [executionError, setExecutionError] = useState<string | null>(null);


  // Try to get data from React Query cache first if it's a fresh execution
  useEffect(() => {
    if (executionIdFromQuery && !historyPath) { // Fresh execution
      const cachedData = queryClient.getQueryData<TestExecutionHistoryData>(['executionResult', executionIdFromQuery]);
      if (cachedData) {
        setLocalHistoryData(cachedData);
      } else {
        // If not in cache (e.g. page refresh), trigger execution or show error
        // For now, this sample won't re-trigger execution on refresh to avoid multiple runs
        // In a real app, might need a way to fetch this specific execution if backend supports it by executionId
         // console.warn(`Execution data for ${executionIdFromQuery} not found in cache. Consider fetching or re-executing.`);
         // Fallback: attempt to execute again if no data, but this is risky.
         // Or simply show an error/prompt.
         // Let's initiate execution if no local history data set from cache
         if (!localHistoryData && !isLoadingExecution) { // Added !isLoadingExecution to prevent multiple calls
            handleExecuteTest();
         }
      }
    }
  }, [executionIdFromQuery, historyPath, queryClient, localHistoryData, isLoadingExecution]); // Added isLoadingExecution to dependencies


  // Fetching specific history file if historyPath is provided
  const { 
    data: fetchedHistoryData, 
    isLoading: isLoadingHistoryFile, 
    error: historyFileError,
    isError: isHistoryFileError,
    refetch: refetchHistoryFile
  } = useQuery({
    queryKey: ['testExecutionHistory', historyPath],
    queryFn: () => getTestExecutionHistoryDetails(historyPath!),
    enabled: !!historyPath,
    retry: false,
  });

  // Fetch test case details for context (e.g. name)
  const {data: testCaseDetails} = useQuery({
    queryKey: ['testCase', projectId, suiteId, testId],
    queryFn: () => getTestCaseById(projectId, suiteId, testId),
    enabled: !!projectId && !!suiteId && !!testId,
  });
  
  const handleExecuteTest = async () => {
    setIsLoadingExecution(true);
    setExecutionError(null);
    setLocalHistoryData(null); // Clear previous local data
    try {
      const response: TestCaseExecutionResponse = await executeTestCase(projectId, suiteId, testId);
      if (response.success && response.result?.history) {
        setLocalHistoryData(response.result.history);
        queryClient.invalidateQueries({ queryKey: ['testCase', projectId, suiteId, testId] }); // Refresh test case data
        queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
         // Update URL to reflect this execution without re-triggering if possible (optional)
        if (response.result.test_id && executionIdFromQuery !== response.result.test_id) {
           router.replace(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?executionId=${response.result.test_id}`, { scroll: false });
        }
      } else {
        setExecutionError(response.error || "Execution failed or returned no history data.");
      }
    } catch (err) {
      setExecutionError(err instanceof Error ? err.message : "An unknown error occurred during execution.");
    } finally {
      setIsLoadingExecution(false);
    }
  };


  // Determine which data to show
  const displayHistoryData = historyPath ? fetchedHistoryData : localHistoryData;
  const isLoading = isLoadingExecution || (historyPath && isLoadingHistoryFile);
  const displayError = executionError || (historyPath && isHistoryFileError ? historyFileError?.message : null);

  // Automatically execute if it's a fresh execution request (no historyPath and executionIdFromQuery is present, but no data yet)
  // This is slightly different from the useEffect above, this is more of an initial trigger.
  useEffect(() => {
    if (executionIdFromQuery && !historyPath && !localHistoryData && !isLoadingExecution && !fetchedHistoryData) {
      handleExecuteTest();
    }
  // Adding dependencies that define the "fresh execution" state
  }, [executionIdFromQuery, historyPath, localHistoryData, isLoadingExecution, fetchedHistoryData]);


  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Test Case
          </Link>
        </Button>
        {!historyPath && ( // Show re-run button only if it's not a specific history view
          <Button onClick={handleExecuteTest} disabled={isLoadingExecution} variant="secondary">
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoadingExecution ? 'animate-spin' : ''}`} />
            {isLoadingExecution ? "Executing..." : "Re-run Test"}
          </Button>
        )}
      </div>

      {testCaseDetails && <h2 className="text-2xl font-semibold mb-1">Test: {testCaseDetails.name}</h2>}
      
      {isLoading && <ExecutionLoadingSkeleton />}
      
      {displayError && (
        <Alert variant="destructive" className="my-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Displaying Execution Details</AlertTitle>
          <AlertDescription>{displayError}</AlertDescription>
           {historyPath && isHistoryFileError && (
            <Button onClick={() => refetchHistoryFile()} variant="outline" size="sm" className="mt-2">
              <RefreshCw className="mr-2 h-4 w-4"/> Retry Fetching History
            </Button>
          )}
        </Alert>
      )}

      {!isLoading && !displayError && !displayHistoryData && (
        <Card className="my-4">
          <CardHeader>
            <CardTitle>No Execution Data</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              {historyPath ? `Could not load history from: ${historyPath}` : "Test execution has not been run or data is not available."}
            </p>
            {!historyPath && ( // Only show "Run Test Now" if it's not a specific history view and not currently loading an execution
              <Button onClick={handleExecuteTest} disabled={isLoadingExecution}>
                <Play className="mr-2 h-4 w-4" /> Run Test Now
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {!isLoading && !displayError && displayHistoryData && (
        <ExecutionDetailsView historyData={displayHistoryData} testCaseId={testId} />
      )}
    </div>
  );
}
