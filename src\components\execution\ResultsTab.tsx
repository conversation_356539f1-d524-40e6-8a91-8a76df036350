"use client";

import type { TestExecutionHistoryData, TestExecutionStepResult } from "@/lib/types";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2, XCircle, Info } from "lucide-react";
import { format } from 'date-fns';
import { ExpandableText } from "@/components/ExpandableContent";

interface ResultsTabProps {
  historyData: TestExecutionHistoryData;
}

function StepResultItem({ result, index }: { result: TestExecutionStepResult; index: number }) {
  const icon = result.success === undefined ? <Info className="h-5 w-5 text-blue-500" /> : 
               result.success ? <CheckCircle2 className="h-5 w-5 text-green-500" /> : 
               <XCircle className="h-5 w-5 text-red-500" />;
  
  return (
    <div className="mb-3 p-3 border rounded-md bg-card">
      <div className="flex items-center font-medium mb-1">
        {icon}
        <span className="ml-2">Step {result.step}</span>
      </div>
      <ExpandableText text={result.content || "No content for this step."} maxLength={150} className="text-sm" contentClassName="text-muted-foreground font-mono text-xs whitespace-pre-wrap"/>
    </div>
  );
}

export function ResultsTab({ historyData }: ResultsTabProps) {
  const { metadata, results } = historyData;

  const statusClass = metadata.success ? "status-success" : "status-error";
  const statusText = metadata.success ? "✅ Test completed successfully" : "❌ Test failed or incomplete";
  const StatusIcon = metadata.success ? CheckCircle2 : XCircle;
  const iconColor = metadata.success ? "text-green-500" : "text-red-500";


  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="glow-text">Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`p-4 rounded-md flex items-center justify-center text-lg font-semibold ${statusClass}`}>
            <StatusIcon className={`mr-2 h-6 w-6 ${iconColor}`} />
            {statusText}
          </div>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-muted/50">
              <CardHeader className="pb-2">
                <CardDescription>Start Time</CardDescription>
                <CardTitle className="text-xl">
                  {metadata.start_time ? format(new Date(metadata.start_time), 'PPpp') : 'N/A'}
                </CardTitle>
              </CardHeader>
            </Card>
            <Card className="bg-muted/50">
              <CardHeader className="pb-2">
                <CardDescription>End Time</CardDescription>
                <CardTitle className="text-xl">
                  {metadata.end_time ? format(new Date(metadata.end_time), 'PPpp') : 'N/A'}
                </CardTitle>
              </CardHeader>
            </Card>
             <Card className="bg-muted/50 md:col-span-2">
              <CardHeader className="pb-2">
                <CardDescription>Total Steps Executed</CardDescription>
                <CardTitle className="text-xl">
                  {metadata.total_steps}
                </CardTitle>
              </CardHeader>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="glow-text">Executed Steps</CardTitle>
        </CardHeader>
        <CardContent>
          {results && results.length > 0 ? (
            results.map((result, index) => (
              <StepResultItem key={index} result={result} index={index} />
            ))
          ) : (
            <p className="text-muted-foreground">No detailed step results available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
