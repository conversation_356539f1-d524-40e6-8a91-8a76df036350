"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  callEnhanceUserStory, 
  callGenerateManualTestCases, 
  callGenerateGherkin, 
  callExecuteSmokeTest, 
  callExecuteFullTest, 
  getProjects,
  getSuitesByProjectId, 
  saveTestHistory 
} from "@/lib/api";
import { 
  Bot, 
  Sparkles, 
  ChevronRight, 
  ChevronLeft, 
  ListChecks, 
  FileText, 
  Lightbulb, 
  Check, 
  Flame, 
  Play, 
  Info, 
  Save 
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import Link from "next/link";
import type { ExecuteSmokeTestOutput } from "@/ai/flows/execute-smoke-test-flow";
import type { Project, TestSuite } from "@/lib/types";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";

type Step = "userStory" | "manualTests" | "gherkin" | "summary";

// Component to display manual test cases in an Excel-like table format
function TestCasesTable({
  testCases,
  onTestCasesChange,
}: {
  testCases: string[];
  onTestCasesChange?: (newTestCases: string[]) => void;
}) {
  const [editableTestCases, setEditableTestCases] = useState<string[]>(testCases);

  const handleTestCaseChange = (index: number, newValue: string) => {
    const newTestCases = [...editableTestCases];
    newTestCases[index] = newValue;
    setEditableTestCases(newTestCases);
    
    if (onTestCasesChange) {
      onTestCasesChange(newTestCases);
    }
  };

  return (
    <div className="border rounded-md overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted">
            <TableHead className="w-14 font-semibold">#</TableHead>
            <TableHead className="font-semibold">Test Case</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {editableTestCases.map((testCase, index) => (
            <TableRow key={index}>
              <TableCell className="font-medium">{index + 1}</TableCell>
              <TableCell>
                {onTestCasesChange ? (
                  <Input
                    value={testCase}
                    onChange={(e) => handleTestCaseChange(index, e.target.value)}
                    className="w-full bg-transparent border-none hover:bg-muted/50 focus:bg-white"
                  />
                ) : (
                  testCase
                )}
              </TableCell>
            </TableRow>
          ))}
          {onTestCasesChange && (
            <TableRow>
              <TableCell colSpan={2} className="text-center">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => {
                    const newTestCases = [...editableTestCases, ""];
                    setEditableTestCases(newTestCases);
                    if (onTestCasesChange) {
                      onTestCasesChange(newTestCases);
                    }
                  }}
                >
                  + Add Test Case
                </Button>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

export default function QaAssistantPage() {
  const { toast } = useToast();

  const [currentStep, setCurrentStep] = useState<Step>("userStory");

  const [originalUserStory, setOriginalUserStory] = useState("");
  const [enhancedUserStory, setEnhancedUserStory] = useState<string | null>(null);
  const [finalUserStory, setFinalUserStory] = useState("");
  const [targetUrl, setTargetUrl] = useState("");

  const [manualTestCasesInput, setManualTestCasesInput] = useState("");
  const [generatedManualTestCases, setGeneratedManualTestCases] = useState<string[] | null>(null);
  const [finalManualTestCases, setFinalManualTestCases] = useState<string[]>([]);
  
  const [gherkinInput, setGherkinInput] = useState("");
  const [generatedGherkin, setGeneratedGherkin] = useState<string | null>(null);
  const [finalGherkin, setFinalGherkin] = useState("");

  const [isLoadingEnhance, setIsLoadingEnhance] = useState(false);
  const [isLoadingManualCases, setIsLoadingManualCases] = useState(false);
  const [isLoadingGherkin, setIsLoadingGherkin] = useState(false);
  const [isLoadingExecution, setIsLoadingExecution] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testExecutionResult, setTestExecutionResult] = useState<any>(null);
  
  // Project saving states
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [suites, setSuites] = useState<TestSuite[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [isLoadingSuites, setIsLoadingSuites] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [selectedSuiteId, setSelectedSuiteId] = useState<string>("");
  const [testName, setTestName] = useState("");
  const [testDescription, setTestDescription] = useState("");

  useEffect(() => {
    // Fetch projects when the dialog is shown
    if (showSaveDialog) {
      fetchProjects();
    }
  }, [showSaveDialog]);

  useEffect(() => {
    // Fetch suites when a project is selected
    if (selectedProjectId) {
      fetchSuitesByProjectId(selectedProjectId);
    } else {
      setSuites([]);
      setSelectedSuiteId("");
    }
  }, [selectedProjectId]);

  const fetchProjects = async () => {
    setIsLoadingProjects(true);
    try {
      const response = await getProjects();
      if (response.items) {
        setProjects(response.items);
      }
    } catch (err) {
      toast({ title: "Error fetching projects", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingProjects(false);
    }
  };

  const fetchSuitesByProjectId = async (projectId: string) => {
    setIsLoadingSuites(true);
    try {
      const response = await getSuitesByProjectId(projectId);
      if (response.items) {
        setSuites(response.items);
      }
    } catch (err) {
      toast({ title: "Error fetching test suites", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingSuites(false);
    }
  };

  const handleEnhanceUserStory = async () => {
    if (!originalUserStory.trim()) {
      toast({ title: "Input Required", description: "Please enter a user story.", variant: "destructive" });
      return;
    }
    setIsLoadingEnhance(true);
    setError(null);
    try {
      const result = await callEnhanceUserStory({ userStory: originalUserStory });
      setEnhancedUserStory(result.enhancedUserStory);
      toast({ title: "User Story Enhanced!" });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Enhancing Story", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingEnhance(false);
    }
  };

  const handleGenerateManualTestCases = async () => {
    if (!finalUserStory.trim()) {
      toast({ title: "User Story Required", description: "A user story is needed to generate test cases.", variant: "destructive" });
      return;
    }
    setIsLoadingManualCases(true);
    setError(null);
    try {
      const result = await callGenerateManualTestCases({ userStory: finalUserStory });
      setGeneratedManualTestCases(result.manualTestCases);
      setManualTestCasesInput(result.manualTestCases.join("\n")); // Populate textarea
      toast({ title: "Manual Test Cases Generated!" });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Generating Test Cases", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingManualCases(false);
    }
  };
  
  const handleGenerateGherkin = async () => {
    if (!finalUserStory.trim() || finalManualTestCases.length === 0 || !targetUrl.trim()) {
      toast({ title: "Inputs Required", description: "User Story, Manual Test Cases, and Target URL are needed.", variant: "destructive" });
      return;
    }
    setIsLoadingGherkin(true);
    setError(null);
    try {
      const instructions = finalManualTestCases.join("\n");
      const result = await callGenerateGherkin({ userStory: finalUserStory, instructions, url: targetUrl });
      setGeneratedGherkin(result.gherkin);
      setGherkinInput(result.gherkin); // Populate textarea
      toast({ title: "Gherkin Scenario Generated!" });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Generating Gherkin", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingGherkin(false);
    }
  };

  const handleExecuteSmokeTest = async () => {
    if (!finalGherkin.trim() || !targetUrl.trim()) {
      toast({ 
        title: "Inputs Required", 
        description: "Gherkin scenario and Target URL are needed for smoke test execution.", 
        variant: "destructive" 
      });
      return;
    }
    
    setIsLoadingExecution(true);
    setError(null);
    try {
      // Call the smoke test API using our API function
      const result = await callExecuteSmokeTest({
        baseUrl: targetUrl,
        instructions: finalManualTestCases.join("\n"),
        userStory: finalUserStory
      });
      
      setTestExecutionResult(result);
      toast({ title: "Smoke Test Executed!", description: "View results in the summary." });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Executing Smoke Test", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingExecution(false);
    }
  };

  const handleExecuteFullTest = async () => {
    if (!finalGherkin.trim() || !targetUrl.trim()) {
      toast({ 
        title: "Inputs Required", 
        description: "Gherkin scenario and Target URL are needed for full test execution.", 
        variant: "destructive" 
      });
      return;
    }
    
    setIsLoadingExecution(true);
    setError(null);
    try {
      // Call the full test API using our API function
      const result = await callExecuteFullTest({
        gherkin: finalGherkin,
        url: targetUrl
      });
      
      setTestExecutionResult(result);
      toast({ title: "Full Test Executed!", description: "View results in the summary." });
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error Executing Full Test", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoadingExecution(false);
    }
  };

  const handleSaveTestHistory = async () => {
    if (!selectedProjectId || !selectedSuiteId || !testName.trim() || !testDescription.trim()) {
      toast({ title: "All fields are required", variant: "destructive" });
      return;
    }
    setIsSaving(true);
    setError(null);
    try {
      await saveTestHistory({
        projectId: selectedProjectId,
        suiteId: selectedSuiteId,
        name: testName,
        description: testDescription,
        gherkin: finalGherkin,
        testHistory: testExecutionResult
      });
      toast({ title: "Test history saved successfully!" });
      setShowSaveDialog(false);
    } catch (err) {
      setError((err as Error).message);
      toast({ title: "Error saving test history", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  const next = () => {
    setError(null);
    if (currentStep === "userStory") {
      if (!originalUserStory.trim() || !targetUrl.trim()) {
        toast({ title: "Input Required", description: "Please provide the original user story and target URL.", variant: "destructive" });
        return;
      }
      setFinalUserStory(enhancedUserStory || originalUserStory);
      setCurrentStep("manualTests");
    } else if (currentStep === "manualTests") {
      const cases = manualTestCasesInput.split('\n').map(s => s.trim()).filter(s => s.length > 0);
      if (cases.length === 0) {
        toast({ title: "Input Required", description: "Please provide or generate manual test cases.", variant: "destructive" });
        return;
      }
      setFinalManualTestCases(cases);
      setCurrentStep("gherkin");
    } else if (currentStep === "gherkin") {
       if (!gherkinInput.trim()) {
        toast({ title: "Input Required", description: "Please provide or generate a Gherkin scenario.", variant: "destructive" });
        return;
      }
      setFinalGherkin(gherkinInput);
      setCurrentStep("summary");
    }
  };

  const prev = () => {
    setError(null);
    if (currentStep === "summary") setCurrentStep("gherkin");
    else if (currentStep === "gherkin") setCurrentStep("manualTests");
    else if (currentStep === "manualTests") setCurrentStep("userStory");
  };

  const renderStep = () => {
    switch (currentStep) {
      case "userStory":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><Lightbulb className="h-6 w-6 text-primary"/>Step 1: User Story & Target URL</CardTitle>
              <CardDescription>Provide the initial user story and the target URL for your test.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="targetUrl">Target URL</Label>
                <Input id="targetUrl" placeholder="https://example.com" value={targetUrl} onChange={(e) => setTargetUrl(e.target.value)} />
              </div>
              <div>
                <Label htmlFor="originalUserStory">Original User Story</Label>
                <Textarea id="originalUserStory" placeholder="As a user, I want to..." value={originalUserStory} onChange={(e) => setOriginalUserStory(e.target.value)} className="min-h-[100px]" />
              </div>
              <Button onClick={handleEnhanceUserStory} disabled={isLoadingEnhance || !originalUserStory.trim()} variant="outline">
                {isLoadingEnhance ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                Enhance with AI
              </Button>
              {enhancedUserStory && (
                <div className="mt-4 p-4 border rounded-md bg-muted">
                  <Label className="font-semibold" htmlFor="editedUserStory">AI Enhanced User Story (you can edit):</Label>
                  <Textarea 
                    id="editedUserStory" 
                    className="min-h-[100px] mt-2 text-sm bg-white" 
                    value={enhancedUserStory} 
                    onChange={(e) => setEnhancedUserStory(e.target.value)} 
                  />
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={next} disabled={!originalUserStory.trim() || !targetUrl.trim()}>Next <ChevronRight className="ml-2 h-4 w-4" /></Button>
            </CardFooter>
          </Card>
        );
      case "manualTests":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><ListChecks className="h-6 w-6 text-primary"/>Step 2: Manual Test Cases</CardTitle>
              <CardDescription>Generate or manually input your test cases based on the user story.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-3 border rounded-md bg-muted/50">
                <Label className="font-semibold">Selected User Story:</Label>
                <p className="text-sm whitespace-pre-wrap">{finalUserStory}</p>
              </div>
               <div className="p-3 border rounded-md bg-muted/50">
                <Label className="font-semibold">Target URL:</Label>
                <p className="text-sm whitespace-pre-wrap">{targetUrl}</p>
              </div>
              <Button onClick={handleGenerateManualTestCases} disabled={isLoadingManualCases} variant="outline">
                {isLoadingManualCases ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                Generate with AI
              </Button>
              <div>
                <Label htmlFor="manualTestCasesInput">Manual Test Cases (one per line)</Label>
                {/* <Textarea id="manualTestCasesInput" value={manualTestCasesInput} onChange={(e) => setManualTestCasesInput(e.target.value)} className="min-h-[150px]" placeholder="1. Navigate to homepage&#10;2. Click login button..."/> */}
                <TestCasesTable 
                  testCases={finalManualTestCases} 
                  onTestCasesChange={(newTestCases) => {
                    setFinalManualTestCases(newTestCases);
                    setManualTestCasesInput(newTestCases.join("\n")); // Update textarea input
                  }} 
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={prev} variant="outline"><ChevronLeft className="mr-2 h-4 w-4" /> Previous</Button>
              <Button onClick={next} disabled={manualTestCasesInput.split('\n').map(s => s.trim()).filter(s => s.length > 0).length === 0}>Next <ChevronRight className="ml-2 h-4 w-4" /></Button>
            </CardFooter>
          </Card>
        );
      case "gherkin":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2"><FileText className="h-6 w-6 text-primary"/>Step 3: Gherkin Scenario</CardTitle>
              <CardDescription>Generate or manually write the Gherkin scenario.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
               <div className="p-3 border rounded-md bg-muted/50 max-h-28 overflow-y-auto">
                <Label className="font-semibold">Selected User Story:</Label>
                <p className="text-sm whitespace-pre-wrap">{finalUserStory}</p>
              </div>
              <div className="p-3 border rounded-md bg-muted/50 max-h-28 overflow-y-auto">
                <Label className="font-semibold">Manual Test Cases:</Label>
                <ul className="list-disc list-inside text-sm">
                    {finalManualTestCases.map((tc, i) => <li key={i}>{tc}</li>)}
                </ul>
              </div>
              <Button onClick={handleGenerateGherkin} disabled={isLoadingGherkin} variant="outline">
                {isLoadingGherkin ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                Generate with AI
              </Button>
              <div>
                <Label htmlFor="gherkinInput">Gherkin Scenario</Label>
                <Textarea id="gherkinInput" value={gherkinInput} onChange={(e) => setGherkinInput(e.target.value)} className="min-h-[200px] font-mono text-sm" placeholder="Feature: User Login..." />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={prev} variant="outline"><ChevronLeft className="mr-2 h-4 w-4" /> Previous</Button>
              <Button onClick={next} disabled={!gherkinInput.trim()}>Finish & View Summary <ChevronRight className="ml-2 h-4 w-4" /></Button>
            </CardFooter>
          </Card>
        );
      case "summary":
        return (
           <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">QA Assistant Summary</CardTitle>
              <CardDescription>Here are the artifacts from your session.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Card className="shadow-none">
                <CardHeader className="pb-2"><CardTitle className="text-lg">Final User Story</CardTitle></CardHeader>
                <CardContent><ScrollArea className="h-24 w-full rounded-md border p-3 bg-muted/30"><pre className="text-sm whitespace-pre-wrap">{finalUserStory}</pre></ScrollArea></CardContent>
              </Card>
               <Card className="shadow-none">
                <CardHeader className="pb-2"><CardTitle className="text-lg">Target URL</CardTitle></CardHeader>
                <CardContent><p className="text-sm p-3 border rounded-md bg-muted/30">{targetUrl}</p></CardContent>
              </Card>
              <Card className="shadow-none">
                <CardHeader className="pb-2"><CardTitle className="text-lg">Manual Test Cases</CardTitle></CardHeader>
                <CardContent>
                  <ScrollArea className="h-40 w-full rounded-md border p-3 bg-muted/30">
                    <ul className="list-disc list-inside text-sm">
                      {finalManualTestCases.map((tc, i) => <li key={i} className="mb-1">{tc}</li>)}
                    </ul>
                  </ScrollArea>
                </CardContent>
              </Card>
              <Card className="shadow-none">
                <CardHeader className="pb-2"><CardTitle className="text-lg">Gherkin Scenario</CardTitle></CardHeader>
                <CardContent><ScrollArea className="h-48 w-full rounded-md border p-3 bg-muted/30"><pre className="text-sm whitespace-pre-wrap font-mono">{finalGherkin}</pre></ScrollArea></CardContent>
              </Card>
              <div className="flex space-x-4">
                <Button onClick={handleExecuteSmokeTest} disabled={isLoadingExecution}>
                  <Flame className="mr-2 h-4 w-4" />
                  Run Smoke Test
                </Button>
                <Button onClick={handleExecuteFullTest} disabled={isLoadingExecution}>
                  <Play className="mr-2 h-4 w-4" />
                  Run Full Test
                </Button>
              </div>
              {testExecutionResult && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Test Execution Complete</AlertTitle>
                  <AlertDescription>
                    <span className={testExecutionResult.metadata.success ? "text-green-500 font-medium" : "text-red-500 font-medium"}>
                      {testExecutionResult.metadata.success ? "✓ Success" : "✗ Failed"}
                    </span> - Total steps: {testExecutionResult.metadata.total_steps}
                    {testExecutionResult.errors.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium">Errors:</p>
                        <ul className="list-disc list-inside">
                          {testExecutionResult.errors.map((error: string, i: number) => (
                            <li key={i} className="text-red-500">{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <div className="mt-2">
                      <Link href="/smoke-test-playground" className="text-blue-500 hover:underline">
                        View full results in Smoke Test Playground
                      </Link>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
              <Alert>
                <Lightbulb className="h-4 w-4" />
                <AlertTitle>Next Steps</AlertTitle>
                <AlertDescription className="flex flex-col gap-2">
                  <p>You can now save these test results to a project.</p>
                  <Button 
                    onClick={() => setShowSaveDialog(true)} 
                    variant="outline"
                    className="w-full sm:w-auto"
                    disabled={!testExecutionResult}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Test Results to Project
                  </Button>
                </AlertDescription>
              </Alert>
            </CardContent>
             <CardFooter className="flex justify-between">
              <Button onClick={prev} variant="outline"><ChevronLeft className="mr-2 h-4 w-4" /> Back to Gherkin</Button>
              {testExecutionResult && (
                <Button 
                  onClick={() => setShowSaveDialog(true)} 
                  className="ml-auto"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save to Project
                </Button>
              )}
            </CardFooter>
          </Card>
        )
      default:
        return null;
    }
  };

  // Progress indicator
  const steps: {id: Step, name: string}[] = [
    { id: "userStory", name: "User Story" },
    { id: "manualTests", name: "Manual Tests" },
    { id: "gherkin", name: "Gherkin" },
    { id: "summary", name: "Summary" },
  ];
  const currentStepIndex = steps.findIndex(s => s.id === currentStep);


  return (
    <div>
      <h1 className="page-header flex items-center gap-2"><Bot /> Guided QA Assistant</h1>
      
      {/* Progress Bar Simple */}
      <div className="mb-8 flex justify-center space-x-2 sm:space-x-4">
        {steps.map((step, index) => (
          <div key={step.id} className="flex flex-col items-center">
            <div className="flex items-center">
              {index > 0 && (
                <div className={cn("h-[2px] w-12 bg-muted", index <= currentStepIndex ? "bg-primary" : "")}></div>
              )}
              <div className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border text-foreground",
                index < currentStepIndex ? "border-primary bg-primary text-primary-foreground" : 
                index === currentStepIndex ? "border-primary" : "border-muted"
              )}>
                {index < currentStepIndex ? <Check className="h-4 w-4" /> : index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={cn("h-[2px] w-12 bg-muted", index < currentStepIndex ? "bg-primary" : "")}></div>
              )}
            </div>
            <div className={cn("mt-2 text-sm", index === currentStepIndex ? "font-medium text-foreground" : "text-muted-foreground")}>
              {step.name}
            </div>
          </div>
        ))}
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>An Error Occurred</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {renderStep()}

      {/* Save Test History Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Save Test History</DialogTitle>
            <DialogDescription>
              Select a project and test suite to save the test history. You can also edit the test name and description.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="projectSelect">Project</Label>
              <Select onValueChange={(value) => setSelectedProjectId(value)} disabled={isLoadingProjects}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.project_id} value={project.project_id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="suiteSelect">Test Suite</Label>
              <Select onValueChange={(value) => setSelectedSuiteId(value)} disabled={isLoadingSuites || suites.length === 0}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a test suite" />
                </SelectTrigger>
                <SelectContent>
                  {suites.map((suite) => (
                    <SelectItem key={suite.suite_id} value={suite.suite_id}>
                      {suite.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="testName">Test Name</Label>
              <Input id="testName" value={testName} onChange={(e) => setTestName(e.target.value)} placeholder="Enter a name for the test" />
            </div>
            <div>
              <Label htmlFor="testDescription">Test Description</Label>
              <Textarea id="testDescription" value={testDescription} onChange={(e) => setTestDescription(e.target.value)} placeholder="Enter a description for the test" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTestHistory} disabled={isSaving}>
              {isSaving ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Save Test History
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  )
}
