"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

export default function ProjectDetailLoading() {
  return (
    <div>
      <Skeleton className="h-9 w-36 mb-4" /> {/* Back button */}
      
      <div className="flex justify-between items-start mb-4">
        <div>
          <Skeleton className="h-10 w-64 mb-1" /> {/* Project Name */}
          <Skeleton className="h-5 w-80 mb-3" /> {/* Project Description */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-24 rounded-full" />
          </div>
          <Skeleton className="h-4 w-72" /> {/* Dates */}
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-9 w-24" /> {/* Edit button */}
          <Skeleton className="h-9 w-28" /> {/* Delete button */}
        </div>
      </div>
      
      <hr className="my-6"/>

      <div className="flex justify-between items-center mb-6">
         <Skeleton className="h-8 w-48" /> {/* Suites Header */}
         <Skeleton className="h-9 w-32" /> {/* Create Suite Button */}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3 mt-1" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-1/4 mb-2" />
              <div className="flex gap-1 mt-1">
                <Skeleton className="h-5 w-12 rounded-full" />
              </div>
              <Skeleton className="h-3 w-1/2 mt-3" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-9 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
