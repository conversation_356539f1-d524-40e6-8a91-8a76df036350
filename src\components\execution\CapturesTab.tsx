"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import Image from 'next/image'; // Using next/image for optimization

interface CapturesTabProps {
  historyData: TestExecutionHistoryData;
}

export function CapturesTab({ historyData }: CapturesTabProps) {
  const { screenshots } = historyData;

  // Helper to check if a string is a Base64 image
  const isBase64Image = (str: string) => str.startsWith('data:image');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="glow-text">Screenshots</CardTitle>
      </CardHeader>
      <CardContent>
        {screenshots && screenshots.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {screenshots.map((screenshot, index) => (
              <div key={index} className="border rounded-lg overflow-hidden shadow-md">
                <CardHeader className="p-3">
                  <CardDescription>Capture {index + 1}</CardDescription>
                </CardHeader>
                <div className="aspect-video relative bg-muted">
                  {isBase64Image(screenshot) || screenshot.startsWith('http') ? (
                     <Image
                      src={screenshot}
                      alt={`Screenshot ${index + 1}`}
                      layout="fill"
                      objectFit="contain"
                      data-ai-hint="screenshot ui"
                    />
                  ) : (
                    // If it's a path, we assume it's relative and needs prefixing or handling by API serving static files.
                    // For this example, let's try a placeholder if it's not a recognizable format.
                    // In a real app, ensure paths are resolvable URLs or serve them.
                    <Image
                      src={`https://placehold.co/600x400.png?text=Path:${screenshot.substring(0,30)}...`}
                      alt={`Screenshot ${index + 1} (Path: ${screenshot})`}
                      layout="fill"
                      objectFit="contain"
                      data-ai-hint="placeholder error"
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">No screenshots available for this execution.</p>
        )}
      </CardContent>
    </Card>
  );
}
