'use server';
/**
 * @fileOverview A flow to generate test code from Gherkin scenarios.
 *
 * - generateCode - A function that generates test code from a Gherkin scenario.
 * - GenerateCodeInput - The input type for the generateCode function.
 * - GenerateCodeOutput - The return type for the generateCode function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateCodeInputSchema = z.object({
  framework: z.string().describe('The testing framework to use (e.g., selenium, playwright, cypress).'),
  gherkin_scenario: z.string().describe('The Gherkin scenario to generate code for.'),
  test_history: z.record(z.any()).optional().describe('Optional history of previous test executions.'),
});
export type GenerateCodeInput = z.infer<typeof GenerateCodeInputSchema>;

const GenerateCodeOutputSchema = z.object({
  code: z.string().describe('The generated test code.'),
});
export type GenerateCodeOutput = z.infer<typeof GenerateCodeOutputSchema>;

export async function generateCode(input: GenerateCodeInput): Promise<GenerateCodeOutput> {
  return generateCodeFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateCodePrompt',
  input: {schema: GenerateCodeInputSchema},
  output: {schema: GenerateCodeOutputSchema},
  prompt: `You are a test automation engineer. Generate test code based on the following Gherkin scenario, using the specified testing framework. Include any necessary imports and setup.

Framework: {{{framework}}}
Gherkin Scenario:
{{{gherkin_scenario}}}

Test History (optional):
{{{test_history}}}

Ensure the generated code is complete and executable.
`,
});

const generateCodeFlow = ai.defineFlow(
  {
    name: 'generateCodeFlow',
    inputSchema: GenerateCodeInputSchema,
    outputSchema: GenerateCodeOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
