"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { TestCase, TestCaseCreateInput, TestCaseUpdateInput } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { XIcon, Bo<PERSON>, Sparkles } from "lucide-react";
import { callEnhanceUserStory, callGenerateGherkin } from "@/lib/api"; // Assuming API functions for AI
import { useToast } from "@/hooks/use-toast";

const testCaseFormSchema = z.object({
  name: z.string().min(2, "Test case name must be at least 2 characters."),
  description: z.string().min(5, "Description must be at least 5 characters."),
  instrucciones: z.string().min(10, "Instructions must be at least 10 characters."),
  historia_de_usuario: z.string().min(10, "User story must be at least 10 characters."),
  gherkin: z.string().optional(),
  url: z.string().url("Must be a valid URL."),
  tags: z.array(z.string()).optional(),
});

export type TestCaseFormData = z.infer<typeof testCaseFormSchema>;

interface TestCaseFormProps {
  testCase?: TestCase;
  onSubmit: (data: TestCaseCreateInput | TestCaseUpdateInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
}

export function TestCaseForm({
  testCase,
  onSubmit,
  isSubmitting,
  submitButtonText = "Create Test Case",
}: TestCaseFormProps) {
  const [currentTag, setCurrentTag] = useState("");
  const { toast } = useToast();
  const [isEnhancingStory, setIsEnhancingStory] = useState(false);
  const [isGeneratingGherkin, setIsGeneratingGherkin] = useState(false);

  const form = useForm<TestCaseFormData>({
    resolver: zodResolver(testCaseFormSchema),
    defaultValues: {
      name: testCase?.name || "",
      description: testCase?.description || "",
      instrucciones: testCase?.instrucciones || "",
      historia_de_usuario: testCase?.historia_de_usuario || "",
      gherkin: testCase?.gherkin || "",
      url: testCase?.url || "",
      tags: testCase?.tags || [],
    },
  });

  const handleAddTag = () => {
    if (currentTag.trim() !== "") {
      const currentTags = form.getValues("tags") || [];
      if (!currentTags.includes(currentTag.trim())) {
        form.setValue("tags", [...currentTags, currentTag.trim()]);
      }
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter(tag => tag !== tagToRemove));
  };

  const handleEnhanceUserStory = async () => {
    const userStory = form.getValues("historia_de_usuario");
    if (!userStory) {
      toast({ title: "User Story Required", description: "Please enter a user story to enhance.", variant: "destructive" });
      return;
    }
    setIsEnhancingStory(true);
    try {
      const result = await callEnhanceUserStory({ userStory });
      form.setValue("historia_de_usuario", result.enhancedUserStory);
      toast({ title: "User Story Enhanced", description: "The user story has been updated." });
    } catch (error) {
      toast({ title: "Error Enhancing Story", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsEnhancingStory(false);
    }
  };

  const handleGenerateGherkin = async () => {
    const userStory = form.getValues("historia_de_usuario");
    const instructions = form.getValues("instrucciones");
    const url = form.getValues("url");

    if (!userStory || !instructions || !url) {
      toast({ title: "Fields Required", description: "User Story, Instructions, and URL are needed to generate Gherkin.", variant: "destructive" });
      return;
    }
    setIsGeneratingGherkin(true);
    try {
      const result = await callGenerateGherkin({ userStory, instructions, url });
      form.setValue("gherkin", result.gherkin);
      toast({ title: "Gherkin Generated", description: "The Gherkin scenario has been generated." });
    } catch (error) {
      toast({ title: "Error Generating Gherkin", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsGeneratingGherkin(false);
    }
  };


  const handleSubmit = async (data: TestCaseFormData) => {
    await onSubmit(data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{testCase ? "Edit Test Case" : "Create New Test Case"}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Test Case Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Valid User Login" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Verify successful login with valid credentials." className="resize-none" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target URL</FormLabel>
                  <FormControl>
                    <Input type="url" placeholder="https://example.com/login" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="historia_de_usuario"
              render={({ field }) => (
                <FormItem>
                  <div className="flex justify-between items-center">
                    <FormLabel>User Story</FormLabel>
                    <Button type="button" variant="outline" size="sm" onClick={handleEnhanceUserStory} disabled={isEnhancingStory}>
                      {isEnhancingStory ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                      Enhance with AI
                    </Button>
                  </div>
                  <FormControl>
                    <Textarea placeholder="As a user, I want to log in so I can access my account." className="resize-none min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="instrucciones"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Instructions / Steps</FormLabel>
                  <FormControl>
                    <Textarea placeholder="1. Navigate to login page...\n2. Enter credentials...\n3. Click login button..." className="resize-none min-h-[120px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="gherkin"
              render={({ field }) => (
                <FormItem>
                   <div className="flex justify-between items-center">
                    <FormLabel>Gherkin Scenario (Optional)</FormLabel>
                     <Button type="button" variant="outline" size="sm" onClick={handleGenerateGherkin} disabled={isGeneratingGherkin}>
                      {isGeneratingGherkin ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Bot className="mr-2 h-4 w-4" />}
                      Generate with AI
                    </Button>
                  </div>
                  <FormControl>
                    <Textarea placeholder="Given I am on the login page..." className="resize-none min-h-[120px] font-mono text-sm" {...field} />
                  </FormControl>
                  <FormDescription>You can write Gherkin manually or generate it using AI based on User Story, Instructions, and URL.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Add a tag (e.g., smoke, critical)"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddTag();
                        }
                      }}
                    />
                    <Button type="button" variant="outline" onClick={handleAddTag}>Add Tag</Button>
                  </div>
                  {field.value && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 text-muted-foreground hover:text-destructive"
                            onClick={() => handleRemoveTag(tag)}
                          >
                            <XIcon size={12} />
                             <span className="sr-only">Remove tag {tag}</span>
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={isSubmitting || isEnhancingStory || isGeneratingGherkin} className="w-full md:w-auto">
              {isSubmitting ? "Submitting..." : submitButtonText}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
