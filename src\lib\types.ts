// API General
export interface ApiResponse<T> {
  success: boolean;
  count?: number;
  items?: T[];
  error?: string;
  details?: string;
}

export interface ApiErrorResponse {
  success: boolean;
  error: string;
  details?: string;
}

// Project
export interface Project {
  project_id: string;
  name: string;
  description: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  test_suites: Record<string, any>; // Consider defining a TestSuiteSummary if shape is known
}

export type ProjectCreateInput = Omit<Project, 'project_id' | 'created_at' | 'updated_at' | 'test_suites'>;
export type ProjectUpdateInput = Partial<Omit<Project, 'project_id' | 'created_at' | 'updated_at' | 'test_suites'>>;

// Test Suite
export interface TestSuite {
  suite_id: string;
  project_id: string; 
  name: string;
  description: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  test_cases?: TestCase[]; // Optional: if suites list endpoint includes test cases
}

export type TestSuiteCreateInput = Omit<TestSuite, 'suite_id' | 'project_id' | 'created_at' | 'updated_at' | 'test_cases'>;
export type TestSuiteUpdateInput = Partial<Omit<TestSuite, 'suite_id' | 'project_id'| 'created_at' | 'updated_at' | 'test_cases'>>;

// Test Case
export type TestCaseStatus = 'Not Executed' | 'Passed' | 'Failed';

export interface TestCase {
  test_id: string;
  suite_id: string; 
  project_id: string; 
  name: string;
  description: string;
  instrucciones: string; // 'instructions' in API doc, but using Spanish to match
  historia_de_usuario: string;
  gherkin?: string;
  url: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  history_files: string[];
  status: TestCaseStatus;
  last_execution: string | null;
  code?: string;
  framework?: string; // "selenium|playwright|cypress"
}

export type TestCaseCreateInput = Omit<TestCase, 'test_id' | 'suite_id' | 'project_id' | 'created_at' | 'updated_at' | 'history_files' | 'status' | 'last_execution' | 'code' | 'framework'>;
export type TestCaseUpdateInput = Partial<TestCaseCreateInput>; // Or more specific if needed
export type TestCaseStatusUpdateInput = { status: TestCaseStatus };

// Test Execution and History
export interface TestExecutionStepResult {
  step: number;
  content: string;
  success?: boolean; 
}

export interface TestExecutionAction {
  step: number;
  type: string;
  details: Record<string, any> | string; 
}

export interface TestExecutionElement {
  step: number;
  tag_name: string;
  xpath: string;
  attributes: Record<string, string>;
}

export interface TestExecutionUrl {
  step: number;
  url: string;
  title: string;
}

export interface TestExecutionMetadata {
  start_time: string | null;
  end_time: string | null;
  total_steps: number;
  success: boolean;
}

export interface TestExecutionHistoryData {
  actions: TestExecutionAction[];
  results: TestExecutionStepResult[];
  elements: TestExecutionElement[];
  urls: TestExecutionUrl[];
  errors: any[]; 
  screenshots: string[]; 
  metadata: TestExecutionMetadata;
  test_id?: string; 
  execution_id?: string; 
  history_path?: string;
  generatedGherkin?: string;
}


export interface TestCaseExecutionResponse {
  success: boolean;
  test_id: string; 
  result?: {
    success: boolean;
    test_id: string; 
    history_path: string;
    history: TestExecutionHistoryData; 
  };
  error: string | null;
}

export interface SuiteExecutionResultItem {
  test_id: string;
  test_name: string;
  result: {
    success: boolean;
    test_id: string; 
    history_path: string;
  };
}

export interface SuiteExecutionResponse {
  success: boolean;
  suite_id: string;
  suite_name: string;
  total_tests: number;
  passed: number;
  failed: number;
  results: SuiteExecutionResultItem[];
  execution_time: string; 
}

// API Health
export interface ApiHealth {
  status: string;
  version: string;
  timestamp: string;
  api_key_configured: boolean;
}

// AI Tool Types
// Imported from corresponding flow files, e.g.,
// import type { EnhanceUserStoryInput, EnhanceUserStoryOutput } from '@/ai/flows/enhance-user-story';
// For GenerateManualTestCases
export type { GenerateManualTestCasesInput, GenerateManualTestCasesOutput } from '@/ai/flows/generate-manual-test-cases';


// For forms
export type ProjectFormData = ProjectCreateInput;
export type SuiteFormData = TestSuiteCreateInput;
export type TestCaseFormData = TestCaseCreateInput;

// Utility types
export interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  items?: NavItem[];
}

