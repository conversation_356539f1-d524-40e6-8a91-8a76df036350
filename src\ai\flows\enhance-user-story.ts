// src/ai/flows/enhance-user-story.ts
'use server';

/**
 * @fileOverview Enhances a user story using AI to improve its clarity and completeness.
 *
 * - enhanceUserStory - A function that enhances a user story.
 * - EnhanceUserStoryInput - The input type for the enhanceUserStory function.
 * - EnhanceUserStoryOutput - The return type for the enhanceUserStory function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const EnhanceUserStoryInputSchema = z.object({
  userStory: z.string().describe('The user story to enhance.'),
  language: z.string().optional().describe('The language to use for the enhanced user story. Defaults to English if not provided.'),
});
export type EnhanceUserStoryInput = z.infer<typeof EnhanceUserStoryInputSchema>;

const EnhanceUserStoryOutputSchema = z.object({
  enhancedUserStory: z.string().describe('The enhanced user story.'),
});
export type EnhanceUserStoryOutput = z.infer<typeof EnhanceUserStoryOutputSchema>;

export async function enhanceUserStory(input: EnhanceUserStoryInput): Promise<EnhanceUserStoryOutput> {
  return enhanceUserStoryFlow(input);
}

const enhanceUserStoryPrompt = ai.definePrompt({
  name: 'enhanceUserStoryPrompt',
  input: {schema: EnhanceUserStoryInputSchema},
  output: {schema: EnhanceUserStoryOutputSchema},
  prompt: `You are an AI assistant that enhances user stories to make them clear and complete for defining test cases.

  Original User Story: {{{userStory}}}
  
  {{#if language}}
  Please enhance the user story in {{language}} language.
  {{/if}}

  Enhanced User Story:`,
});

const enhanceUserStoryFlow = ai.defineFlow(
  {
    name: 'enhanceUserStoryFlow',
    inputSchema: EnhanceUserStoryInputSchema,
    outputSchema: EnhanceUserStoryOutputSchema,
  },
  async input => {
    const {output} = await enhanceUserStoryPrompt(input);
    return output!;
  }
);
