"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { callGenerateGherkin, callGenerateCode, callEnhanceUserStory } from "@/lib/api";
import type { GenerateGherkinInput, GenerateCodeInput, EnhanceUserStoryInput } from "@/lib/types"; // Assuming these are exported or defined based on AI flows
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText, Code2, Lightbulb } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

// Schemas for forms
const enhanceStorySchema = z.object({
  userStory: z.string().min(10, "User story must be at least 10 characters."),
});
type EnhanceStoryFormData = z.infer<typeof enhanceStorySchema>;

const generateGherkinSchema = z.object({
  userStory: z.string().min(10, "User story is required."),
  instructions: z.string().min(10, "Instructions are required."),
  url: z.string().url("A valid URL is required."),
});
type GenerateGherkinFormData = z.infer<typeof generateGherkinSchema>;

const generateCodeSchema = z.object({
  framework: z.enum(["selenium", "playwright", "cypress"]),
  gherkin_scenario: z.string().min(20, "Gherkin scenario must be at least 20 characters."),
  test_history: z.string().optional().describe("JSON string of test history (optional)"),
});
type GenerateCodeFormData = z.infer<typeof generateCodeSchema>;


export default function AiToolsPage() {
  const { toast } = useToast();
  
  const [enhanceResult, setEnhanceResult] = useState<string | null>(null);
  const [gherkinResult, setGherkinResult] = useState<string | null>(null);
  const [codeResult, setCodeResult] = useState<string | null>(null);

  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isGeneratingGherkin, setIsGeneratingGherkin] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);

  const enhanceForm = useForm<EnhanceStoryFormData>({ resolver: zodResolver(enhanceStorySchema) });
  const gherkinForm = useForm<GenerateGherkinFormData>({ resolver: zodResolver(generateGherkinSchema) });
  const codeForm = useForm<GenerateCodeFormData>({ resolver: zodResolver(generateCodeSchema) });

  const onEnhanceSubmit = async (data: EnhanceStoryFormData) => {
    setIsEnhancing(true);
    setEnhanceResult(null);
    try {
      const result = await callEnhanceUserStory(data);
      setEnhanceResult(result.enhancedUserStory);
      toast({ title: "User Story Enhanced Successfully!" });
    } catch (error) {
      toast({ title: "Error Enhancing Story", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsEnhancing(false);
    }
  };

  const onGherkinSubmit = async (data: GenerateGherkinFormData) => {
    setIsGeneratingGherkin(true);
    setGherkinResult(null);
    try {
      const result = await callGenerateGherkin(data);
      setGherkinResult(result.gherkin);
      toast({ title: "Gherkin Generated Successfully!" });
    } catch (error) {
      toast({ title: "Error Generating Gherkin", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsGeneratingGherkin(false);
    }
  };

  const onCodeSubmit = async (data: GenerateCodeFormData) => {
    setIsGeneratingCode(true);
    setCodeResult(null);
    let testHistoryObj: Record<string, any> | undefined = undefined;
    if (data.test_history) {
      try {
        testHistoryObj = JSON.parse(data.test_history);
      } catch (e) {
        toast({ title: "Invalid JSON in Test History", description: "Please provide valid JSON or leave empty.", variant: "destructive" });
        setIsGeneratingCode(false);
        return;
      }
    }
    const payload: GenerateCodeInput = { ...data, test_history: testHistoryObj };

    try {
      const result = await callGenerateCode(payload);
      setCodeResult(result.code);
      toast({ title: "Code Generated Successfully!" });
    } catch (error) {
      toast({ title: "Error Generating Code", description: (error as Error).message, variant: "destructive" });
    } finally {
      setIsGeneratingCode(false);
    }
  };

  const AiToolCard = ({ title, description, icon, children }: { title: string, description: string, icon: React.ReactNode, children: React.ReactNode }) => (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {icon} {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );

  const ResultDisplay = ({ result, language }: { result: string | null, language?: string }) => (
    result && (
      <Card className="mt-4 bg-muted">
        <CardHeader><CardTitle className="text-lg">Generated Output</CardTitle></CardHeader>
        <CardContent>
          <ScrollArea className="h-60 w-full rounded-md border p-3">
            <pre><code className={language ? `language-${language}` : ''}>{result}</code></pre>
          </ScrollArea>
        </CardContent>
      </Card>
    )
  );


  return (
    <div>
      <h1 className="page-header flex items-center gap-2"><Bot /> AI-Powered Tools</h1>
      <Tabs defaultValue="enhance-story" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="enhance-story"><Lightbulb className="mr-2 h-4 w-4" />Enhance User Story</TabsTrigger>
          <TabsTrigger value="generate-gherkin"><FileText className="mr-2 h-4 w-4" />Generate Gherkin</TabsTrigger>
          <TabsTrigger value="generate-code"><Code2 className="mr-2 h-4 w-4" />Generate Code</TabsTrigger>
        </TabsList>

        <TabsContent value="enhance-story">
          <AiToolCard title="Enhance User Story" description="Improve the clarity and completeness of your user stories with AI." icon={<Lightbulb className="h-6 w-6 text-primary"/>}>
            <Form {...enhanceForm}>
              <form onSubmit={enhanceForm.handleSubmit(onEnhanceSubmit)} className="space-y-4">
                <FormField control={enhanceForm.control} name="userStory" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Original User Story</FormLabel>
                    <FormControl><Textarea placeholder="e.g., I want to login" {...field} className="min-h-[100px]"/></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <Button type="submit" disabled={isEnhancing}>
                  {isEnhancing ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                  Enhance Story
                </Button>
              </form>
            </Form>
            <ResultDisplay result={enhanceResult} />
          </AiToolCard>
        </TabsContent>

        <TabsContent value="generate-gherkin">
          <AiToolCard title="Generate Gherkin Scenario" description="Automatically create Gherkin scenarios from user stories, instructions, and target URL." icon={<FileText className="h-6 w-6 text-primary"/>}>
            <Form {...gherkinForm}>
              <form onSubmit={gherkinForm.handleSubmit(onGherkinSubmit)} className="space-y-4">
                <FormField control={gherkinForm.control} name="userStory" render={({ field }) => (
                  <FormItem>
                    <FormLabel>User Story</FormLabel>
                    <FormControl><Textarea placeholder="As a user, I want to..." {...field} className="min-h-[80px]" /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={gherkinForm.control} name="instructions" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instructions / Test Steps</FormLabel>
                    <FormControl><Textarea placeholder="1. Go to X page..." {...field} className="min-h-[100px]" /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={gherkinForm.control} name="url" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target URL</FormLabel>
                    <FormControl><Input type="url" placeholder="https://example.com" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                <Button type="submit" disabled={isGeneratingGherkin}>
                  {isGeneratingGherkin ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                  Generate Gherkin
                </Button>
              </form>
            </Form>
            <ResultDisplay result={gherkinResult} language="gherkin" />
          </AiToolCard>
        </TabsContent>
        
        <TabsContent value="generate-code">
          <AiToolCard title="Generate Test Code" description="Convert Gherkin scenarios into executable test code for various frameworks." icon={<Code2 className="h-6 w-6 text-primary"/>}>
            <Form {...codeForm}>
              <form onSubmit={codeForm.handleSubmit(onCodeSubmit)} className="space-y-4">
                <FormField control={codeForm.control} name="framework" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Testing Framework</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl><SelectTrigger><SelectValue placeholder="Select a framework" /></SelectTrigger></FormControl>
                      <SelectContent>
                        <SelectItem value="selenium">Selenium</SelectItem>
                        <SelectItem value="playwright">Playwright</SelectItem>
                        <SelectItem value="cypress">Cypress</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )} />
                <FormField control={codeForm.control} name="gherkin_scenario" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gherkin Scenario</FormLabel>
                    <FormControl><Textarea placeholder="Given I am on the homepage..." {...field} className="min-h-[150px] font-mono text-sm" /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
                 <FormField control={codeForm.control} name="test_history" render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test History (Optional JSON)</FormLabel>
                    <FormControl><Textarea placeholder='{ "urls": ["https://..."], ... }' {...field} className="min-h-[100px] font-mono text-xs" /></FormControl>
                    <FormDescription>Provide previous test execution history as a JSON string if available.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )} />
                <Button type="submit" disabled={isGeneratingCode}>
                  {isGeneratingCode ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                  Generate Code
                </Button>
              </form>
            </Form>
            <ResultDisplay result={codeResult} language="javascript" /> {/* Adjust language based on framework */}
          </AiToolCard>
        </TabsContent>
      </Tabs>
    </div>
  );
}
