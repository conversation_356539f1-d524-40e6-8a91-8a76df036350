"use client";

import { useState, type ReactNode } from 'react';
import { Button } from '@/components/ui/button';

interface ExpandableContentProps {
  shortContent: ReactNode;
  fullContent: ReactNode;
  buttonTexts?: {
    showMore: string;
    showLess: string;
  };
  className?: string;
}

export function ExpandableContent({
  shortContent,
  fullContent,
  buttonTexts = { showMore: 'Show more', showLess: 'Show less' },
  className,
}: ExpandableContentProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={className}>
      <div>{isExpanded ? fullContent : shortContent}</div>
      <Button
        variant="link"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="p-0 h-auto mt-1 text-primary hover:text-primary/80"
      >
        {isExpanded ? buttonTexts.showLess : buttonTexts.showMore}
      </Button>
    </div>
  );
}

interface ExpandableTextProps {
  text: string;
  maxLength?: number;
  buttonTexts?: {
    showMore: string;
    showLess: string;
  };
  className?: string;
  contentClassName?: string;
}

export function ExpandableText({
  text,
  maxLength = 200,
  buttonTexts = { showMore: 'Show more', showLess: 'Show less' },
  className,
  contentClassName = "font-mono text-sm whitespace-pre-wrap"
}: ExpandableTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (text.length <= maxLength) {
    return <div className={`${className} ${contentClassName}`}>{text}</div>;
  }

  const shortText = text.substring(0, maxLength) + "...";

  return (
    <div className={className}>
      <div className={contentClassName}>
        {isExpanded ? text : shortText}
      </div>
      <Button
        variant="link"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="p-0 h-auto mt-1 text-primary hover:text-primary/80"
      >
        {isExpanded ? buttonTexts.showLess : buttonTexts.showMore}
      </Button>
    </div>
  );
}
