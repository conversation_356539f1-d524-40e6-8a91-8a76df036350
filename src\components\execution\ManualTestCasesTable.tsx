"use client";

import { <PERSON>, Table<PERSON>ody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";

interface ManualTestCasesTableProps {
  testCases: string[];
}

interface TestCaseRow {
  id: number;
  step: string;
  expected: string;
  testData?: string;
  comments?: string;
}

function parseTestCasesToRows(testCases: string[]): TestCaseRow[] {
  return testCases.map((testCase, index) => {
    // Improved parsing logic that looks for "Test Step:" and "Expected:" patterns
    let step = testCase;
    let expected = "";
    let testData = "";
    
    // Check for "Test Step:" pattern
    if (testCase.includes("Test Step:")) {
      const stepParts = testCase.split("Test Step:");
      if (stepParts.length > 1) {
        step = stepParts[1].split("Expected:")[0].trim();
      }
    }
    
    // Check for "Expected:" pattern
    if (testCase.includes("Expected:")) {
      const expectedParts = testCase.split("Expected:");
      if (expectedParts.length > 1) {
        expected = expectedParts[1].trim();
        
        // If we still have the original step, update it
        if (step === testCase) {
          step = expectedParts[0].replace("Test Step:", "").trim();
        }
      }
    }
    
    // If no "Expected:" keyword but there's a possible separator like "->" or ":"
    if (!expected && (testCase.includes("->") || (testCase.includes(":") && !testCase.includes("Test Step:")))) {
      const separator = testCase.includes("->") ? "->" : ":";
      const parts = testCase.split(separator);
      if (parts.length > 1) {
        step = parts[0].trim();
        expected = parts.slice(1).join(separator).trim();
      }
    }
    
    return {
      id: index + 1,
      step: step,
      expected: expected,
      testData: testData,
      comments: ""
    };
  });
}

export function ManualTestCasesTable({ testCases }: ManualTestCasesTableProps) {
  const [rows, setRows] = useState<TestCaseRow[]>(() => parseTestCasesToRows(testCases));
  
  const handleCellChange = (id: number, field: keyof TestCaseRow, value: string) => {
    setRows(prev => 
      prev.map(row => 
        row.id === id ? { ...row, [field]: value } : row
      )
    );
  };

  return (
    <div className="w-full">
      <ScrollArea className="h-[400px] rounded-md border border-border">
        <Table className="excel-style-table">
          <TableHeader className="bg-muted/50 sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[50px]">ID</TableHead>
              <TableHead className="w-[40%]">Test Step</TableHead>
              <TableHead className="w-[30%]">Expected Result</TableHead>
              <TableHead className="w-[15%]">Test Data</TableHead>
              <TableHead className="w-[15%]">Comments</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((row) => (
              <TableRow key={row.id} className="excel-row hover:bg-muted/50">
                <TableCell className="font-medium">{row.id}</TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(row.id, "step", e.currentTarget.textContent || "")}
                  >
                    {row.step}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(row.id, "expected", e.currentTarget.textContent || "")}
                  >
                    {row.expected}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(row.id, "testData", e.currentTarget.textContent || "")}
                  >
                    {row.testData}
                  </div>
                </TableCell>
                <TableCell>
                  <div 
                    contentEditable 
                    suppressContentEditableWarning 
                    className="p-1 border-none focus:ring-1 focus:ring-primary rounded-md min-h-[30px] hover:bg-card"
                    onBlur={(e) => handleCellChange(row.id, "comments", e.currentTarget.textContent || "")}
                  >
                    {row.comments}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
    </div>
  );
}
