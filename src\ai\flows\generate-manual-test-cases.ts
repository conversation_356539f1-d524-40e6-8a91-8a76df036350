// src/ai/flows/generate-manual-test-cases.ts
'use server';
/**
 * @fileOverview A Genkit flow to generate manual test case steps from a user story.
 *
 * - generateManualTestCases - A function that generates manual test case steps.
 * - GenerateManualTestCasesInput - The input type for the generateManualTestCases function.
 * - GenerateManualTestCasesOutput - The return type for the generateManualTestCases function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateManualTestCasesInputSchema = z.object({
  userStory: z.string().describe('The user story to generate manual test cases from.'),
  language: z.string().optional().describe('The language to use for the generated manual test cases. Defaults to English if not provided.'),
});
export type GenerateManualTestCasesInput = z.infer<typeof GenerateManualTestCasesInputSchema>;

const GenerateManualTestCasesOutputSchema = z.object({
  manualTestCases: z.array(z.string().describe("A detailed manual test case step or description. Each string in the array represents a distinct test case or a major step.")).describe('An array of generated manual test case steps.'),
});
export type GenerateManualTestCasesOutput = z.infer<typeof GenerateManualTestCasesOutputSchema>;

export async function generateManualTestCases(input: GenerateManualTestCasesInput): Promise<GenerateManualTestCasesOutput> {
  return generateManualTestCasesFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateManualTestCasesPrompt',
  input: {schema: GenerateManualTestCasesInputSchema},
  output: {schema: GenerateManualTestCasesOutputSchema},
  prompt: `You are a QA expert specializing in creating comprehensive manual test cases.
Based on the following user story, generate a list of clear, actionable, and step-by-step manual test cases.
Each item in the 'manualTestCases' array should be a distinct test case or a significant step.
Focus on covering different scenarios, including positive paths, negative paths, and edge cases where applicable.

For each test case, use the format of "Test Step: [action to take] Expected: [expected result]" to make parsing easier.

User Story:
{{{userStory}}}

{{#if language}}
Generate the manual test cases in {{language}} language.
{{/if}}

Generate the manual test cases:
`,
});

const generateManualTestCasesFlow = ai.defineFlow(
  {
    name: 'generateManualTestCasesFlow',
    inputSchema: GenerateManualTestCasesInputSchema,
    outputSchema: GenerateManualTestCasesOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    if (!output) {
      throw new Error('AI failed to generate manual test cases.');
    }
    return output;
  }
);
