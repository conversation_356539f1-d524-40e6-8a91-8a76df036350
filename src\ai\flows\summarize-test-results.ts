'use server';

/**
 * @fileOverview Summarizes test results using AI to identify key trends and issues.
 *
 * - summarizeTestResults - A function that handles the summarization of test results.
 * - SummarizeTestResultsInput - The input type for the summarizeTestResults function.
 * - SummarizeTestResultsOutput - The return type for the summarizeTestResults function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SummarizeTestResultsInputSchema = z.object({
  testResults: z
    .string()
    .describe('A string containing the test results to summarize.'),
});
export type SummarizeTestResultsInput = z.infer<typeof SummarizeTestResultsInputSchema>;

const SummarizeTestResultsOutputSchema = z.object({
  summary: z.string().describe('A summary of the test results.'),
});
export type SummarizeTestResultsOutput = z.infer<typeof SummarizeTestResultsOutputSchema>;

export async function summarizeTestResults(input: SummarizeTestResultsInput): Promise<SummarizeTestResultsOutput> {
  return summarizeTestResultsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'summarizeTestResultsPrompt',
  input: {schema: SummarizeTestResultsInputSchema},
  output: {schema: SummarizeTestResultsOutputSchema},
  prompt: `You are an AI assistant that summarizes test results and identifies key trends and issues.

  Summarize the following test results:

  {{testResults}}`,
});

const summarizeTestResultsFlow = ai.defineFlow(
  {
    name: 'summarizeTestResultsFlow',
    inputSchema: SummarizeTestResultsInputSchema,
    outputSchema: SummarizeTestResultsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
