"use client";

import Link from 'next/link';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getTestCaseById, deleteTestCase, executeTestCase, updateTestCaseStatus } from '@/lib/api';
import type { TestCase, TestCaseExecutionResponse, TestCaseStatus } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertD<PERSON>ogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { TestTube2, ArrowLeft, Edit, Trash2, Play, AlertCircle, CheckCircle, XCircle, Clock, FileText, History } from 'lucide-react';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

function TestCaseDetailSkeleton() {
  return (
    <div>
      <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <Skeleton className="h-10 w-3/4 mb-1" /> {/* TC Name */}
          <Skeleton className="h-5 w-full mb-3" /> {/* TC Description */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
          <Skeleton className="h-4 w-72 mb-1" /> {/* Dates */}
          <Skeleton className="h-6 w-24 rounded-full mt-2" /> {/* Status Badge */}
        </div>
        <div className="flex flex-col items-end gap-2">
            <div className="flex gap-2">
                <Skeleton className="h-9 w-24" /> {/* Edit button */}
                <Skeleton className="h-9 w-28" /> {/* Delete button */}
            </div>
            <Skeleton className="h-9 w-40" /> {/* Execute Test Case button */}
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader>
            <CardContent><Skeleton className="h-20 w-full" /></CardContent>
          </Card>
        ))}
      </div>
      <Separator className="my-8" />
      <Skeleton className="h-8 w-48 mb-4" /> {/* History Header */}
      <Card><CardContent className="p-6"><Skeleton className="h-10 w-full" /></CardContent></Card>
    </div>
  );
}

export default function TestCaseDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;

  const { data: testCase, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['testCase', projectId, suiteId, testId],
    queryFn: () => getTestCaseById(projectId, suiteId, testId),
    enabled: !!projectId && !!suiteId && !!testId,
  });

  const deleteMutation = useMutation({
    mutationFn: () => deleteTestCase(projectId, suiteId, testId),
    onSuccess: () => {
      toast({ title: "Test Case Deleted", description: `Test Case "${testCase?.name}" has been deleted.` });
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      router.push(`/projects/${projectId}/suites/${suiteId}`);
    },
    onError: (err) => {
      toast({ title: "Error Deleting Test Case", description: err.message, variant: "destructive" });
    },
  });

  const executeMutation = useMutation({
    mutationFn: () => executeTestCase(projectId, suiteId, testId),
    onSuccess: (data: TestCaseExecutionResponse) => {
      toast({
        title: "Test Execution Initiated",
        description: `Execution for "${testCase?.name}" started.`,
      });
      // After execution, redirect to the results page
      // Pass execution data via query params or state management if it's large / complex
      // For simplicity, using query params for history_path if available
      // The API response includes the full history, so we can pass that.
      // Store it in a temporary client-side cache or pass via router state if supported by Next.js App Router easily.
      // A simple way is to navigate and then let the target page fetch if needed, or if result is small, pass it.
      // Given the structure, /execute/page.tsx will handle displaying the results.
      // We can set a query param to indicate it's a fresh execution.
      queryClient.setQueryData(['executionResult', data.result?.test_id || Date.now()], data.result?.history);
      router.push(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?executionId=${data.result?.test_id || 'latest'}`);
      refetch(); // Refetch test case to update status and last execution
    },
    onError: (err) => {
      toast({ title: "Error Executing Test Case", description: err.message, variant: "destructive" });
    },
  });

  const updateStatusMutation = useMutation({
    mutationFn: (newStatus: TestCaseStatus) => updateTestCaseStatus(projectId, suiteId, testId, { status: newStatus }),
    onSuccess: (updatedTc) => {
      queryClient.setQueryData(['testCase', projectId, suiteId, testId], updatedTc);
      queryClient.invalidateQueries({queryKey: ['testCases', projectId, suiteId]});
      toast({ title: "Status Updated", description: `Test Case status changed to ${updatedTc.status}.` });
    },
    onError: (err) => {
      toast({ title: "Error Updating Status", description: err.message, variant: "destructive" });
    }
  });


  if (isLoading) return <TestCaseDetailSkeleton />;
  if (isError) return <Alert variant="destructive"><AlertCircle className="h-4 w-4" /><AlertTitle>Error</AlertTitle><AlertDescription>{error.message}</AlertDescription></Alert>;
  if (!testCase) return <p>Test Case not found.</p>;

  let StatusIcon = Clock;
  if (testCase.status === 'Passed') StatusIcon = CheckCircle;
  else if (testCase.status === 'Failed') StatusIcon = XCircle;

  const detailSections = [
    { title: "Instructions", content: testCase.instrucciones, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "User Story", content: testCase.historia_de_usuario, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "Target URL", content: testCase.url, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "Gherkin Scenario", content: testCase.gherkin || "Not specified", icon: <FileText className="h-5 w-5 text-primary" /> },
  ];

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>

      <div className="flex flex-col md:flex-row justify-between md:items-start mb-4">
        <div className="flex-1">
          <h1 className="page-header mb-1 flex items-center gap-2">
            <TestTube2 className="h-8 w-8 text-primary" />
            {testCase.name}
          </h1>
          <p className="text-muted-foreground text-sm mb-3">{testCase.description}</p>
          <div className="flex flex-wrap gap-2 mb-2">
            {testCase.tags.map((tag) => (
              <Badge key={tag} variant="outline">{tag}</Badge>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Created: {format(new Date(testCase.created_at), 'PPP p')} | Updated: {format(new Date(testCase.updated_at), 'PPP p')}
          </p>
           {testCase.last_execution && (
            <p className="text-xs text-muted-foreground mt-1">
              Last Execution: {format(new Date(testCase.last_execution), 'PPP p')}
            </p>
          )}
          <div className="mt-3 flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
             <Select
                value={testCase.status}
                onValueChange={(value: TestCaseStatus) => updateStatusMutation.mutate(value)}
                disabled={updateStatusMutation.isPending}
              >
                <SelectTrigger className="w-[180px] h-8">
                  <div className="flex items-center">
                    <StatusIcon className={`mr-2 h-4 w-4 ${
                      testCase.status === 'Passed' ? 'text-green-500' : 
                      testCase.status === 'Failed' ? 'text-red-500' : 'text-gray-500'
                    }`} />
                    <SelectValue placeholder="Set status" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Not Executed"><Clock className="mr-2 h-4 w-4 text-gray-500 inline-block"/>Not Executed</SelectItem>
                  <SelectItem value="Passed"><CheckCircle className="mr-2 h-4 w-4 text-green-500 inline-block"/>Passed</SelectItem>
                  <SelectItem value="Failed"><XCircle className="mr-2 h-4 w-4 text-red-500 inline-block"/>Failed</SelectItem>
                </SelectContent>
              </Select>
          </div>
        </div>
         <div className="flex flex-col items-start md:items-end gap-2 mt-4 md:mt-0">
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled> {/* TODO: Link to edit page */}
              <Edit className="mr-2 h-4 w-4" /> Edit
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the test case "{testCase.name}".
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => deleteMutation.mutate()} disabled={deleteMutation.isPending}>
                    {deleteMutation.isPending ? "Deleting..." : "Delete Test Case"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
          <Button 
            size="sm" 
            onClick={() => executeMutation.mutate()} 
            disabled={executeMutation.isPending}
            className="w-full md:w-auto bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"
          >
            <Play className="mr-2 h-4 w-4" /> 
            {executeMutation.isPending ? "Executing..." : "Execute Test Case"}
          </Button>
        </div>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {detailSections.map(section => (
          <Card key={section.title}>
            <CardHeader className="flex flex-row items-center gap-2 pb-2">
              {section.icon}
              <CardTitle className="text-lg">{section.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm whitespace-pre-wrap font-sans bg-muted p-3 rounded-md max-h-60 overflow-y-auto">{section.content}</pre>
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator className="my-8" />

      <div>
        <h2 className="section-header mb-4 flex items-center gap-2"><History className="h-6 w-6 text-primary"/>Execution History</h2>
        {testCase.history_files && testCase.history_files.length > 0 ? (
          <ul className="space-y-2">
            {testCase.history_files.map((file, index) => (
              <li key={index} className="p-3 border rounded-md bg-card hover:bg-muted/50">
                <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?historyPath=${encodeURIComponent(file)}`} className="text-sm text-primary hover:underline">
                  Execution Record: {file.split('/').pop() || `History ${index + 1}`}
                </Link>
                 {/* TODO: Add date/status if available for each history file */}
              </li>
            ))}
          </ul>
        ) : (
          <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
              No execution history available for this test case.
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
