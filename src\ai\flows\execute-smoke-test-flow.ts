// src/ai/flows/execute-smoke-test-flow.ts
'use server';
/**
 * @fileOverview A Genkit flow to execute a smoke test based on user inputs.
 * It generates a Gherkin scenario and simulates its execution, returning detailed history data.
 *
 * - executeSmokeTest - A function that handles the smoke test execution.
 * - ExecuteSmokeTestInput - The input type for the executeSmokeTest function.
 * - ExecuteSmokeTestOutput - The return type for the executeSmokeTest function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { TestExecutionHistoryDataSchema, type TestExecutionHistoryData } from '@/ai/schemas/test-execution-history-schema';

const ExecuteSmokeTestInputSchema = z.object({
  baseUrl: z.string().url().describe('The base URL of the application to test.'),
  instructions: z.string().min(10).describe('Detailed instructions or steps for the smoke test. This should describe the key actions and verifications to perform.'),
  userStory: z.string().optional().describe('An optional user story to provide context for the smoke test (e.g., "As a user, I want to ensure the main login flow is working").'),
});
export type ExecuteSmokeTestInput = z.infer<typeof ExecuteSmokeTestInputSchema>;

// The output is the detailed execution history itself
export type ExecuteSmokeTestOutput = TestExecutionHistoryData;


export async function executeSmokeTest(input: ExecuteSmokeTestInput): Promise<ExecuteSmokeTestOutput> {
  const result = await executeSmokeTestFlow(input);
  // The flow directly returns TestExecutionHistoryData, so we ensure it matches the type.
  // The schema validation happens within the prompt definition.
  return result as ExecuteSmokeTestOutput;
}

const smokeTestPrompt = ai.definePrompt({
  name: 'smokeTestExecutionPrompt',
  input: {schema: ExecuteSmokeTestInputSchema},
  output: {schema: TestExecutionHistoryDataSchema},
  prompt: `You are an expert QA automation engineer tasked with performing a simulated smoke test and documenting its execution.
Given the Base URL, Instructions, and an optional User Story:

1.  **Generate a Gherkin Scenario:** Based on the inputs, create a concise Gherkin scenario (Feature, Scenario, Given, When, Then steps) that covers the smoke test. Include this as 'generatedGherkin' in the output. The Gherkin should be a single string.
2.  **Simulate Execution:** "Execute" this Gherkin scenario step-by-step. For each step:
    *   Describe the action taken (populate 'actions' array: type, details). Examples for 'type': 'navigate', 'click', 'typeText', 'verifyElementPresent', 'verifyText'.
    *   Describe the expected outcome and observed result (populate 'results' array: content, success).
    *   If an action interacts with a UI element, describe a plausible element (populate 'elements' array: tag_name, xpath, attributes).
    *   If an action involves a URL, record it (populate 'urls' array: url, title).
3.  **Populate Metadata:**
    *   'start_time' and 'end_time': Use placeholder ISO 8601 timestamps (e.g., "2024-01-01T10:00:00Z" and "2024-01-01T10:01:00Z").
    *   'total_steps': Count of Gherkin steps.
    *   'success': Overall success (true if all 'Then' steps in Gherkin likely passed, false otherwise).
4.  **Output Format:** Ensure the entire output STRICTLY adheres to the TestExecutionHistoryDataSchema.
    *   `screenshots` array should be empty.
    *   `errors` array should contain strings describing any simulated failures or issues. If a 'Then' step fails, include an error message.
    *   `test_id`, `execution_id`, `history_path` should be omitted or null where optional, as this is a transient playground execution.

Inputs:
Base URL: {{{baseUrl}}}
Instructions:
{{{instructions}}}
{{#if userStory}}
User Story: {{{userStory}}}
{{/if}}

Begin simulation and provide the output in the specified JSON format.
`,
});

const executeSmokeTestFlow = ai.defineFlow(
  {
    name: 'executeSmokeTestFlow',
    inputSchema: ExecuteSmokeTestInputSchema,
    outputSchema: TestExecutionHistoryDataSchema, // Output is directly the history data
  },
  async (input: ExecuteSmokeTestInput) => {
    const {output} = await smokeTestPrompt(input);
    if (!output) {
      throw new Error("AI failed to generate smoke test execution details.");
    }
    // Add the user story to the output if available
    if (input.userStory) {
      output.userStory = input.userStory;
    }
    // The prompt is defined to output TestExecutionHistoryDataSchema directly
    return output;
  }
);
