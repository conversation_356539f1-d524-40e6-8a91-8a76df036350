"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Copy } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";

interface GherkinHighlighterProps {
  gherkinCode: string;
}

export function GherkinHighlighter({ gherkinCode }: GherkinHighlighterProps) {
  const { toast } = useToast();
  const [highlightedGherkin, setHighlightedGherkin] = useState<string>(gherkinCode);
  
  useEffect(() => {
    // Apply syntax highlighting to the Gherkin code
    let highlighted = gherkinCode;
    
    // Highlight "Feature:" keyword
    highlighted = highlighted.replace(
      /(Feature:.*?)(\n|$)/g, 
      '<span class="text-primary font-bold">$1</span>$2'
    );
    
    // Highlight "Scenario:" keyword
    highlighted = highlighted.replace(
      /(<PERSON><PERSON><PERSON>:.*?)(\n|$)/g, 
      '<span class="text-violet-400 dark:text-violet-300 font-bold">$1</span>$2'
    );
    
    // Highlight "Given" keyword
    highlighted = highlighted.replace(
      /(Given\s+.*?)(\n|$)/g, 
      '<span class="text-emerald-500 dark:text-emerald-300 font-semibold">$1</span>$2'
    );
    
    // Highlight "When" keyword
    highlighted = highlighted.replace(
      /(When\s+.*?)(\n|$)/g, 
      '<span class="text-amber-500 dark:text-amber-300 font-semibold">$1</span>$2'
    );
    
    // Highlight "Then" keyword
    highlighted = highlighted.replace(
      /(Then\s+.*?)(\n|$)/g, 
      '<span class="text-rose-500 dark:text-rose-300 font-semibold">$1</span>$2'
    );
    
    // Highlight "And" and "But" keywords
    highlighted = highlighted.replace(
      /(And\s+.*?)(\n|$)/g, 
      '<span class="text-sky-400 font-semibold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(But\s+.*?)(\n|$)/g, 
      '<span class="text-sky-400 font-semibold">$1</span>$2'
    );
    
    // Highlight Scenario Outline
    highlighted = highlighted.replace(
      /(Scenario Outline:.*?)(\n|$)/g, 
      '<span class="text-fuchsia-400 font-bold">$1</span>$2'
    );
    
    // Highlight Examples
    highlighted = highlighted.replace(
      /(Examples:.*?)(\n|$)/g, 
      '<span class="text-indigo-400 font-bold">$1</span>$2'
    );
    
    // Highlight Spanish keywords if present
    highlighted = highlighted.replace(
      /(Característica:.*?)(\n|$)/g, 
      '<span class="text-primary font-bold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Escenario:.*?)(\n|$)/g, 
      '<span class="text-violet-400 dark:text-violet-300 font-bold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Dado\s+.*?)(\n|$)/g, 
      '<span class="text-emerald-500 dark:text-emerald-300 font-semibold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Cuando\s+.*?)(\n|$)/g, 
      '<span class="text-amber-500 dark:text-amber-300 font-semibold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Entonces\s+.*?)(\n|$)/g, 
      '<span class="text-rose-500 dark:text-rose-300 font-semibold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Y\s+.*?)(\n|$)/g, 
      '<span class="text-sky-400 font-semibold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Pero\s+.*?)(\n|$)/g, 
      '<span class="text-sky-400 font-semibold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Esquema del escenario:.*?)(\n|$)/g, 
      '<span class="text-fuchsia-400 font-bold">$1</span>$2'
    );
    highlighted = highlighted.replace(
      /(Ejemplos:.*?)(\n|$)/g, 
      '<span class="text-indigo-400 font-bold">$1</span>$2'
    );
    
    setHighlightedGherkin(highlighted);
  }, [gherkinCode]);

  const handleCopyToClipboard = () => {
    // Copy the raw Gherkin code (without HTML)
    navigator.clipboard.writeText(gherkinCode)
      .then(() => toast({ title: "Copied to Clipboard", description: "Gherkin copied." }))
      .catch(() => toast({ title: "Copy Failed", description: "Could not copy Gherkin.", variant: "destructive" }));
  };

  return (
    <div className="mt-1 flex items-start gap-2">
      <ScrollArea className="h-[300px] w-full rounded-md border bg-muted/30">
        <div 
          className="p-4 font-mono text-sm whitespace-pre-wrap"
          dangerouslySetInnerHTML={{ __html: highlightedGherkin }}
        />
      </ScrollArea>
      <Button variant="ghost" size="icon" onClick={handleCopyToClipboard}>
        <Copy className="h-4 w-4" />
      </Button>
    </div>
  );
}
