"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";

interface MetadataTabProps {
  historyData: TestExecutionHistoryData;
  testIdFromUrl?: string; // The actual test case ID from URL params
}

interface MetadataItemProps {
  label: string;
  value: string | number | null | undefined;
}

function MetadataItem({ label, value }: MetadataItemProps) {
  return (
    <Card className="bg-muted/50">
      <CardHeader className="p-4">
        <CardDescription>{label}</CardDescription>
        <CardTitle className="text-lg truncate" title={String(value ?? 'N/A')}>{value ?? 'N/A'}</CardTitle>
      </CardHeader>
    </Card>
  );
}

export function MetadataTab({ historyData, testIdFromUrl }: MetadataTabProps) {
  const { metadata, actions, elements, urls, screenshots, test_id, execution_id, history_path } = historyData;

  const items = [
    { label: "Test Case ID (from execution data)", value: test_id },
    { label: "Test Case ID (from URL)", value: testIdFromUrl },
    { label: "Execution ID / Timestamp", value: execution_id },
    { label: "History File Path", value: history_path },
    { label: "Total Steps Defined", value: metadata.total_steps },
    { label: "Actions Executed", value: actions?.length || 0 },
    { label: "Elements Interacted", value: elements?.length || 0 },
    { label: "URLs Visited", value: urls?.length || 0 },
    { label: "Screenshots Taken", value: screenshots?.length || 0 },
    { label: "Start Time", value: metadata.start_time },
    { label: "End Time", value: metadata.end_time },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="glow-text">Technical Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {items.map(item => (
            <MetadataItem key={item.label} label={item.label} value={item.value} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
