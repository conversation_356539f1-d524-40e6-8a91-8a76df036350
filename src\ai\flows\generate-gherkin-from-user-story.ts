// This file is machine-generated - edit at your own risk!

'use server';
/**
 * @fileOverview This file contains a Genkit flow for generating Gherkin scenarios from user stories.
 *
 * - generateGherkin - A function that generates a Gherkin scenario from a user story.
 * - GenerateGherkinInput - The input type for the generateGherkin function.
 * - GenerateGherkinOutput - The return type for the generateGherkin function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateGherkinInputSchema = z.object({
  instructions: z.string().describe('The instructions for the test case.'),
  url: z.string().describe('The URL of the application to test.'),
  userStory: z.string().describe('The user story to generate a Gherkin scenario from.'),
  language: z.string().optional().describe('The language to use for the generated Gherkin scenario. Defaults to English if not provided.'),
});
export type GenerateGherkinInput = z.infer<typeof GenerateGherkinInputSchema>;

const GenerateGherkinOutputSchema = z.object({
  gherkin: z.string().describe('The generated Gherkin scenario.'),
});
export type GenerateGherkinOutput = z.infer<typeof GenerateGherkinOutputSchema>;

export async function generateGherkin(input: GenerateGherkinInput): Promise<GenerateGherkinOutput> {
  return generateGherkinFlow(input);
}

const generateGherkinPrompt = ai.definePrompt({
  name: 'generateGherkinPrompt',
  input: {schema: GenerateGherkinInputSchema},
  output: {schema: GenerateGherkinOutputSchema},
  prompt: `You are a test automation expert who translates user stories into Gherkin scenarios.

Convert the following user story into a Gherkin scenario, using the provided instructions and URL to create a comprehensive test case.

User Story: {{{userStory}}}
Instructions: {{{instructions}}}
URL: {{{url}}}
{{#if language}}
Please generate the Gherkin scenario in {{language}} language.
{{/if}}

Gherkin Scenario:
`,
});

const generateGherkinFlow = ai.defineFlow(
  {
    name: 'generateGherkinFlow',
    inputSchema: GenerateGherkinInputSchema,
    outputSchema: GenerateGherkinOutputSchema,
  },
  async input => {
    const {output} = await generateGherkinPrompt(input);
    return output!;
  }
);


































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































generateGherkinFlow.ts
"""

import {ai} from './genkit';
import {z} from 'genkit';

const InstructionSchema = z.object({
  instructions: z.string().describe('The instructions for the test case'),
  url: z.string().describe('The URL of the application to test'),
  userStory: z.string().describe('The user story to generate a Gherkin scenario from'),
});

const ResponseSchema = z.object({
  gherkin: z.string().describe('The generated Gherkin scenario')
});

/**
 * Generates a Gherkin scenario from a user story.
 */
export const generateGherkinFromUserStory = ai.defineFlow(
  {
    name: 'generateGherkinFromUserStoryFlow',
    inputSchema: InstructionSchema,
    outputSchema: ResponseSchema,
    description: 'Generates a Gherkin scenario from a user story.'
  },
  async input => {
    const prompt = ai.definePrompt({
      name: 'generateGherkinPrompt',
      input: {schema: InstructionSchema},
      output: {schema: ResponseSchema},
      prompt: `You are a test automation expert who translates user stories into Gherkin scenarios.\n\nConvert the following user story into a Gherkin scenario, using the provided instructions and URL to create a comprehensive test case.\n\nUser Story: {{{userStory}}}\nInstructions: {{{instructions}}}\nURL: {{{url}}}\n\nGherkin Scenario:`
    });

    const response = await prompt(input);

    return {
      gherkin: response.output!.gherkin
    };
  }
);
